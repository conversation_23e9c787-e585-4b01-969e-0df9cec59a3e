import React from 'react';

const AboutSection = () => {
  return (
    <section id="about" className="py-20 bg-black text-white">
      <div className="container mx-auto px-6 max-w-6xl">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left side - Content */}
          <div className="space-y-8">
            {/* Section Badge */}
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-gray-900 rounded-full border border-gray-800">
              <span className="text-2xl">🎬</span>
              <span className="text-sm font-medium text-gray-300">About</span>
            </div>

            {/* Heading */}
            <h2 className="text-4xl lg:text-5xl font-bold leading-tight">
              Who We Are
            </h2>

            {/* Content */}
            <div className="space-y-6">
              <p className="text-lg text-gray-300 leading-relaxed">
                We're a creative team of editors passionate about transforming videos into powerful stories. From YouTubers and influencers to brands and corporates, we've worked with diverse clients across industries.
              </p>
              
              <p className="text-lg text-gray-300 leading-relaxed">
                Our mission? To make your content look professional, engaging, and unforgettable.
              </p>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-6 pt-8">
              <div className="text-center">
                <div className="text-3xl font-bold text-green-400">500+</div>
                <div className="text-sm text-gray-400 mt-1">Projects Completed</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-400">50+</div>
                <div className="text-sm text-gray-400 mt-1">Happy Clients</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-400">24/7</div>
                <div className="text-sm text-gray-400 mt-1">Support</div>
              </div>
            </div>
          </div>

          {/* Right side - Visual Element */}
          <div className="relative">
            {/* Main card */}
            <div className="bg-gradient-to-br from-gray-900 to-gray-800 rounded-2xl p-8 border border-gray-700 relative overflow-hidden">
              {/* Background pattern */}
              <div className="absolute inset-0 opacity-10">
                <div className="absolute top-4 left-4 w-20 h-20 border-2 border-green-400 rounded-full"></div>
                <div className="absolute bottom-4 right-4 w-16 h-16 bg-green-400 rounded-full opacity-20"></div>
                <div className="absolute top-1/2 right-8 w-8 h-8 bg-green-400 rounded-full opacity-30"></div>
              </div>

              {/* Content */}
              <div className="relative z-10">
                <div className="flex items-center gap-4 mb-6">
                  <div className="w-12 h-12 bg-green-400 rounded-full flex items-center justify-center">
                    <span className="text-black text-xl font-bold">✓</span>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold">Quality Guaranteed</h3>
                    <p className="text-gray-400 text-sm">Professional results every time</p>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <span className="text-gray-300">Creative storytelling</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <span className="text-gray-300">Fast turnaround</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <span className="text-gray-300">Unlimited revisions</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <span className="text-gray-300">Industry expertise</span>
                  </div>
                </div>

                {/* Call to action */}
                <div className="mt-8 pt-6 border-t border-gray-700">
                  <button className="w-full bg-green-400 text-black font-semibold py-3 px-6 rounded-lg hover:bg-green-300 transition-colors duration-300">
                    Start Your Project
                  </button>
                </div>
              </div>
            </div>

            {/* Floating elements */}
            <div className="absolute -top-4 -right-4 w-24 h-24 bg-green-400 rounded-full opacity-20 animate-pulse"></div>
            <div className="absolute -bottom-6 -left-6 w-32 h-32 border-2 border-green-400 rounded-full opacity-30"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
