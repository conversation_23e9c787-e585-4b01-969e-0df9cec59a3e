import React from 'react';

const AboutSection = () => {
  return (
    <section id="about" className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 text-white relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0">
        {/* Scattered dots */}
        <div className="absolute top-20 left-10 w-2 h-2 bg-blue-400 rounded-full opacity-60"></div>
        <div className="absolute top-40 right-20 w-1 h-1 bg-white rounded-full opacity-40"></div>
        <div className="absolute bottom-40 left-20 w-1.5 h-1.5 bg-blue-300 rounded-full opacity-50"></div>
        <div className="absolute top-60 left-1/4 w-1 h-1 bg-white rounded-full opacity-30"></div>
        <div className="absolute bottom-60 right-1/3 w-2 h-2 bg-blue-500 rounded-full opacity-40"></div>
        <div className="absolute top-80 right-10 w-1 h-1 bg-white rounded-full opacity-50"></div>
      </div>

      <div className="container mx-auto px-6 py-20 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Left side - Content */}
          <div className="space-y-8">
            {/* Section Badge */}
            <div className="inline-block">
              <span className="text-sm font-medium text-gray-400 bg-gray-800 px-4 py-2 rounded-full border border-gray-700">
                About
              </span>
            </div>

            {/* Main Heading */}
            <div className="space-y-4">
              <h2 className="text-5xl lg:text-6xl font-bold leading-tight">
                Powered by Results
              </h2>
              <h3 className="text-3xl lg:text-4xl font-bold text-orange-400 italic">
                Built on Integrity
              </h3>
            </div>

            {/* Content */}
            <div className="space-y-6">
              <p className="text-lg text-gray-300 leading-relaxed">
                We're a creative team of editors passionate about transforming videos into powerful stories. From YouTubers and influencers to brands and corporates, we've worked with diverse clients across industries.
              </p>

              <p className="text-lg text-gray-300 leading-relaxed">
                Our mission? To make your content look professional, engaging, and unforgettable.
              </p>
            </div>

            {/* Contact Button */}
            <div className="pt-4">
              <button className="bg-transparent border-2 border-white text-white px-8 py-3 rounded-full font-semibold hover:bg-white hover:text-black transition-all duration-300">
                Contact Us
              </button>
            </div>
          </div>

          {/* Right side - Dashboard Cards */}
          <div className="relative">
            {/* 3D Rocket */}
            <div className="absolute top-10 right-20 z-20">
              <div className="text-6xl transform rotate-12">🚀</div>
            </div>

            {/* Average Increase Card */}
            <div className="bg-gray-800 rounded-2xl p-6 mb-6 border border-gray-700 relative">
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-white font-semibold">Average Increase</h4>
              </div>

              {/* Chart bars */}
              <div className="flex items-end gap-2 mb-4 h-20">
                <div className="bg-gray-600 w-4 h-8 rounded-t"></div>
                <div className="bg-gray-600 w-4 h-12 rounded-t"></div>
                <div className="bg-gray-600 w-4 h-6 rounded-t"></div>
                <div className="bg-gray-600 w-4 h-16 rounded-t"></div>
                <div className="bg-blue-500 w-4 h-20 rounded-t"></div>
              </div>

              {/* Client avatars and rating */}
              <div className="flex items-center justify-between">
                <div className="flex -space-x-2">
                  <div className="w-8 h-8 bg-gray-600 rounded-full border-2 border-gray-800"></div>
                  <div className="w-8 h-8 bg-gray-500 rounded-full border-2 border-gray-800"></div>
                  <div className="w-8 h-8 bg-gray-400 rounded-full border-2 border-gray-800"></div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-semibold">32k+Clients</div>
                  <div className="text-xs text-yellow-400">⭐ 4.7 Rating</div>
                </div>
              </div>
            </div>

            {/* Quick Growth Card */}
            <div className="bg-gray-800 rounded-2xl p-6 border border-gray-700 relative">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-white font-semibold mb-2">Quick Growth Your Business With SEO</h4>
                </div>
                <div className="w-10 h-10 bg-gray-700 rounded-full flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Stats */}
        <div className="grid grid-rows-2 md:grid-rows-4 gap-6 mt-20 flex">
          <div className="bg-gray-800 rounded-2xl p-6 text-center border border-gray-700">
            <div className="text-3xl font-bold text-white mb-2">120+</div>
            <div className="text-sm text-gray-400">Awards & Honours</div>
          </div>
          <div className="bg-gray-800 rounded-2xl p-6 text-center border border-gray-700">
            <div className="text-3xl font-bold text-white mb-2">98%</div>
            <div className="text-sm text-gray-400">Client Satisfied</div>
          </div>
          <div className="bg-gray-800 rounded-2xl p-6 text-center border border-gray-700">
            <div className="text-3xl font-bold text-white mb-2">14K</div>
            <div className="text-sm text-gray-400">Realized Projects</div>
          </div>
          <div className="bg-gray-800 rounded-2xl p-6 text-center border border-gray-700">
            <div className="text-3xl font-bold text-white mb-2">140%</div>
            <div className="text-sm text-gray-400">Conversion Increased</div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
