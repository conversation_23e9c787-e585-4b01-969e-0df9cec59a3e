import React, { useState } from 'react';

const PortfolioSection = () => {
  const [activeCategory, setActiveCategory] = useState('all');

  const categories = [
    { id: 'all', name: 'All Work' },
    { id: 'youtube', name: 'YouTube' },
    { id: 'reels', name: 'Reels & Shorts' },
    { id: 'corporate', name: 'Corporate' },
    { id: 'events', name: 'Events' }
  ];

  const portfolioItems = [
    {
      id: 1,
      title: "Tech Startup Launch",
      category: "corporate",
      thumbnail: "/api/placeholder/400/300",
      videoUrl: "https://www.youtube.com/embed/dQw4w9WgXcQ",
      client: "TechCorp",
      views: "2.5M"
    },
    {
      id: 2,
      title: "Travel Vlog Series",
      category: "youtube",
      thumbnail: "/api/placeholder/400/300",
      videoUrl: "https://www.youtube.com/embed/dQw4w9WgXcQ",
      client: "Adventure Channel",
      views: "1.8M"
    },
    {
      id: 3,
      title: "Product Launch Reel",
      category: "reels",
      thumbnail: "/api/placeholder/400/300",
      videoUrl: "https://www.youtube.com/embed/dQw4w9WgXcQ",
      client: "Fashion Brand",
      views: "850K"
    },
    {
      id: 4,
      title: "Wedding Highlights",
      category: "events",
      thumbnail: "/api/placeholder/400/300",
      videoUrl: "https://www.youtube.com/embed/dQw4w9WgXcQ",
      client: "Private Client",
      views: "125K"
    },
    {
      id: 5,
      title: "Gaming Content",
      category: "youtube",
      thumbnail: "/api/placeholder/400/300",
      videoUrl: "https://www.youtube.com/embed/dQw4w9WgXcQ",
      client: "GameStream",
      views: "3.2M"
    },
    {
      id: 6,
      title: "Conference Recap",
      category: "corporate",
      thumbnail: "/api/placeholder/400/300",
      videoUrl: "https://www.youtube.com/embed/dQw4w9WgXcQ",
      client: "Business Summit",
      views: "450K"
    }
  ];

  const clients = [
    { name: "TechCorp", logo: "/api/placeholder/120/60" },
    { name: "Adventure Channel", logo: "/api/placeholder/120/60" },
    { name: "Fashion Brand", logo: "/api/placeholder/120/60" },
    { name: "GameStream", logo: "/api/placeholder/120/60" },
    { name: "Business Summit", logo: "/api/placeholder/120/60" },
    { name: "Creative Agency", logo: "/api/placeholder/120/60" },
    { name: "Startup Inc", logo: "/api/placeholder/120/60" },
    { name: "Media House", logo: "/api/placeholder/120/60" }
  ];

  const filteredItems = activeCategory === 'all' 
    ? portfolioItems 
    : portfolioItems.filter(item => item.category === activeCategory);

  return (
    <section id="portfolio" className="py-20 bg-black text-white">
      <div className="container mx-auto px-6 max-w-7xl">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-block mb-4">
            <span className="text-sm font-medium text-gray-400 bg-gray-800 px-4 py-2 rounded-full border border-gray-700">
              Portfolio
            </span>
          </div>
          <h2 className="text-4xl lg:text-5xl font-bold mb-6">
            Our Portfolio
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Discover our latest work and see how we've helped brands and creators tell their stories through compelling video content.
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setActiveCategory(category.id)}
              className={`px-6 py-3 rounded-full font-semibold transition-all duration-300 ${
                activeCategory === category.id
                  ? 'bg-green-400 text-black'
                  : 'bg-gray-800 text-gray-300 hover:bg-gray-700 border border-gray-600'
              }`}
            >
              {category.name}
            </button>
          ))}
        </div>

        {/* Portfolio Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {filteredItems.map((item) => (
            <div 
              key={item.id}
              className="bg-gray-900 rounded-2xl overflow-hidden border border-gray-800 hover:border-green-400 transition-all duration-300 group hover:transform hover:scale-105"
            >
              {/* Video Thumbnail */}
              <div className="relative aspect-video bg-gray-800 overflow-hidden">
                <img 
                  src={item.thumbnail} 
                  alt={item.title}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-black/50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <button className="bg-green-400 text-black w-16 h-16 rounded-full flex items-center justify-center hover:bg-green-300 transition-colors duration-300">
                    <svg className="w-6 h-6 ml-1" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M8 5v14l11-7z"/>
                    </svg>
                  </button>
                </div>
              </div>

              {/* Content */}
              <div className="p-6">
                <h3 className="text-xl font-bold mb-2 group-hover:text-green-400 transition-colors duration-300">
                  {item.title}
                </h3>
                <div className="flex justify-between items-center text-sm text-gray-400 mb-4">
                  <span>{item.client}</span>
                  <span>{item.views} views</span>
                </div>
                <button className="w-full bg-transparent border-2 border-green-400 text-green-400 py-2 px-4 rounded-lg font-semibold hover:bg-green-400 hover:text-black transition-all duration-300">
                  Watch Project
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Client Logos */}
        <div className="text-center">
          <h3 className="text-2xl font-bold mb-8">Trusted by Amazing Clients</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-8 items-center opacity-60">
            {clients.map((client, index) => (
              <div key={index} className="bg-gray-800 rounded-lg p-4 hover:opacity-100 transition-opacity duration-300">
                <img 
                  src={client.logo} 
                  alt={client.name}
                  className="w-full h-8 object-contain filter grayscale hover:grayscale-0 transition-all duration-300"
                />
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default PortfolioSection;
