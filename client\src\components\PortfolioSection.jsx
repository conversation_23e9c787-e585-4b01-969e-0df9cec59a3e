import React, { useState } from 'react';

const PortfolioSection = () => {
  const [activeCategory, setActiveCategory] = useState('all');

  const categories = [
    { id: 'all', name: 'All Work', count: 12 },
    { id: 'youtube', name: 'YouTube', count: 5 },
    { id: 'reels', name: 'Reels & Shorts', count: 3 },
    { id: 'corporate', name: 'Corporate', count: 2 },
    { id: 'events', name: 'Events', count: 2 }
  ];

  const portfolioItems = [
    {
      id: 1,
      title: "Tech Startup Launch Video",
      category: "corporate",
      thumbnail: "https://images.unsplash.com/photo-1551434678-e076c223a692?w=400&h=300&fit=crop",
      videoUrl: "https://www.youtube.com/embed/dQw4w9WgXcQ",
      client: "TechCorp",
      views: "2.5M",
      duration: "3:45",
      featured: true
    },
    {
      id: 2,
      title: "Adventure Travel Vlog",
      category: "youtube",
      thumbnail: "https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=400&h=300&fit=crop",
      videoUrl: "https://www.youtube.com/embed/dQw4w9WgXcQ",
      client: "Adventure Channel",
      views: "1.8M",
      duration: "12:30",
      featured: false
    },
    {
      id: 3,
      title: "Fashion Product Launch",
      category: "reels",
      thumbnail: "https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=300&fit=crop",
      videoUrl: "https://www.youtube.com/embed/dQw4w9WgXcQ",
      client: "Fashion Brand",
      views: "850K",
      duration: "0:30",
      featured: false
    },
    {
      id: 4,
      title: "Wedding Highlight Reel",
      category: "events",
      thumbnail: "https://images.unsplash.com/photo-1519741497674-611481863552?w=400&h=300&fit=crop",
      videoUrl: "https://www.youtube.com/embed/dQw4w9WgXcQ",
      client: "Private Client",
      views: "125K",
      duration: "5:20",
      featured: false
    },
    {
      id: 5,
      title: "Gaming Montage Series",
      category: "youtube",
      thumbnail: "https://images.unsplash.com/photo-1542751371-adc38448a05e?w=400&h=300&fit=crop",
      videoUrl: "https://www.youtube.com/embed/dQw4w9WgXcQ",
      client: "GameStream",
      views: "3.2M",
      duration: "8:15",
      featured: true
    },
    {
      id: 6,
      title: "Business Conference Recap",
      category: "corporate",
      thumbnail: "https://images.unsplash.com/photo-1540575467063-178a50c2df87?w=400&h=300&fit=crop",
      videoUrl: "https://www.youtube.com/embed/dQw4w9WgXcQ",
      client: "Business Summit",
      views: "450K",
      duration: "4:10",
      featured: false
    }
  ];

  const clients = [
    { name: "TechCorp", logo: "https://via.placeholder.com/120x60/4F46E5/FFFFFF?text=TechCorp" },
    { name: "Adventure Channel", logo: "https://via.placeholder.com/120x60/059669/FFFFFF?text=Adventure" },
    { name: "Fashion Brand", logo: "https://via.placeholder.com/120x60/DC2626/FFFFFF?text=Fashion" },
    { name: "GameStream", logo: "https://via.placeholder.com/120x60/7C3AED/FFFFFF?text=GameStream" },
    { name: "Business Summit", logo: "https://via.placeholder.com/120x60/1F2937/FFFFFF?text=Business" },
    { name: "Creative Agency", logo: "https://via.placeholder.com/120x60/F59E0B/FFFFFF?text=Creative" },
    { name: "Startup Inc", logo: "https://via.placeholder.com/120x60/10B981/FFFFFF?text=Startup" },
    { name: "Media House", logo: "https://via.placeholder.com/120x60/3B82F6/FFFFFF?text=Media" }
  ];

  const filteredItems = activeCategory === 'all'
    ? portfolioItems
    : portfolioItems.filter(item => item.category === activeCategory);

  return (
    <section id="portfolio" className="py-20 bg-white">
      <div className="container mx-auto px-6 max-w-7xl">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-blue-50 text-blue-600 px-4 py-2 rounded-full text-sm font-medium mb-4">
            <span>🎥</span>
            Our Portfolio
          </div>
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Our Latest Work
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Discover our latest projects and see how we've helped brands and creators tell their stories through compelling video content.
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setActiveCategory(category.id)}
              className={`px-6 py-3 rounded-full font-semibold transition-all duration-300 flex items-center gap-2 ${
                activeCategory === category.id
                  ? 'bg-blue-600 text-white shadow-lg'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-300'
              }`}
            >
              {category.name}
              <span className={`text-xs px-2 py-1 rounded-full ${
                activeCategory === category.id
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-200 text-gray-600'
              }`}>
                {category.count}
              </span>
            </button>
          ))}
        </div>

        {/* Portfolio Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {filteredItems.map((item) => (
            <div
              key={item.id}
              className={`bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 group hover:transform hover:scale-105 ${
                item.featured ? 'ring-2 ring-blue-500' : 'border border-gray-200'
              }`}
            >
              {/* Featured Badge */}
              {item.featured && (
                <div className="absolute z-10 top-4 left-4 bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                  Featured
                </div>
              )}

              {/* Video Thumbnail */}
              <div className="relative aspect-video overflow-hidden">
                <img
                  src={item.thumbnail}
                  alt={item.title}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                />

                {/* Duration Badge */}
                <div className="absolute bottom-4 right-4 bg-black/80 text-white px-2 py-1 rounded text-sm font-medium">
                  {item.duration}
                </div>

                {/* Play Button Overlay */}
                <div className="absolute inset-0 bg-black/40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <button className="bg-white text-blue-600 w-16 h-16 rounded-full flex items-center justify-center hover:bg-blue-50 transition-colors duration-300 shadow-lg">
                    <svg className="w-6 h-6 ml-1" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M8 5v14l11-7z"/>
                    </svg>
                  </button>
                </div>
              </div>

              {/* Content */}
              <div className="p-6">
                <div className="flex items-center gap-2 mb-3">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    item.category === 'youtube' ? 'bg-red-100 text-red-600' :
                    item.category === 'reels' ? 'bg-pink-100 text-pink-600' :
                    item.category === 'corporate' ? 'bg-blue-100 text-blue-600' :
                    'bg-green-100 text-green-600'
                  }`}>
                    {categories.find(cat => cat.id === item.category)?.name}
                  </span>
                </div>

                <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors duration-300">
                  {item.title}
                </h3>

                <div className="flex justify-between items-center text-sm text-gray-500 mb-4">
                  <span className="font-medium">{item.client}</span>
                  <span>{item.views} views</span>
                </div>

                <button className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors duration-300">
                  Watch Project
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Client Logos */}
        <div className="bg-gray-50 rounded-3xl p-12">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">Trusted by Amazing Clients</h3>
            <p className="text-gray-600">We're proud to work with leading brands and creators worldwide</p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-8 items-center">
            {clients.map((client, index) => (
              <div key={index} className="bg-white rounded-lg p-4 shadow-sm hover:shadow-md transition-all duration-300 group">
                <img
                  src={client.logo}
                  alt={client.name}
                  className="w-full h-8 object-contain opacity-60 group-hover:opacity-100 transition-opacity duration-300"
                />
              </div>
            ))}
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center mt-16">
          <h3 className="text-3xl font-bold text-gray-900 mb-4">
            Ready to Showcase Your Story?
          </h3>
          <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
            Let's create something amazing together. Get started with your video project today and join our portfolio of success stories.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors duration-300">
              Start Your Project
            </button>
            <button className="border-2 border-blue-600 text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition-all duration-300">
              View All Work
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PortfolioSection;
