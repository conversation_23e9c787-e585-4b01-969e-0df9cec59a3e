import React, { useState } from 'react';

const TestimonialsSection = () => {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);

  const testimonials = [
    {
      id: 1,
      name: "<PERSON>",
      role: "YouTube Creator",
      company: "Adventure Vlogs",
      image: "/api/placeholder/80/80",
      rating: 5,
      text: "They transformed my raw clips into a professional YouTube video that got 5x more engagement! The editing style perfectly matched my brand, and the turnaround was incredibly fast.",
      videoViews: "2.5M",
      project: "Travel Vlog Series"
    },
    {
      id: 2,
      name: "<PERSON>",
      role: "Marketing Director",
      company: "TechStart Inc.",
      image: "/api/placeholder/80/80",
      rating: 5,
      text: "Outstanding work on our product launch video. The team understood our vision immediately and delivered a cinematic piece that exceeded our expectations. Our conversion rate increased by 40%!",
      videoViews: "850K",
      project: "Product Launch Campaign"
    },
    {
      id: 3,
      name: "<PERSON>",
      role: "Content Creator",
      company: "Fashion Forward",
      image: "/api/placeholder/80/80",
      rating: 5,
      text: "The reels they edited for us went viral! Their understanding of current trends and ability to create engaging content is unmatched. We've seen a 300% increase in followers.",
      videoViews: "1.2M",
      project: "Instagram Reels Campaign"
    },
    {
      id: 4,
      name: "<PERSON>",
      role: "CEO",
      company: "Business Solutions Ltd.",
      image: "/api/placeholder/80/80",
      rating: 5,
      text: "Professional, reliable, and creative. They handled our corporate video with such attention to detail. The final product perfectly represented our brand values and professionalism.",
      videoViews: "450K",
      project: "Corporate Brand Video"
    },
    {
      id: 5,
      name: "Lisa Park",
      role: "Event Coordinator",
      company: "Dream Weddings",
      image: "/api/placeholder/80/80",
      rating: 5,
      text: "They captured the magic of our wedding perfectly! The highlight reel brought tears to our eyes. Every important moment was beautifully edited with perfect music synchronization.",
      videoViews: "125K",
      project: "Wedding Highlight Reel"
    },
    {
      id: 6,
      name: "Alex Kumar",
      role: "Gaming Streamer",
      company: "GameMaster Pro",
      image: "/api/placeholder/80/80",
      rating: 5,
      text: "Best gaming content editors I've worked with! They know exactly how to cut highlights that keep viewers engaged. My average watch time increased by 60% after working with them.",
      videoViews: "3.2M",
      project: "Gaming Highlights Series"
    }
  ];

  const nextTestimonial = () => {
    setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  const currentClient = testimonials[currentTestimonial];

  return (
    <section id="testimonials" className="py-20 bg-gray-900 text-white">
      <div className="container mx-auto px-6 max-w-7xl">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-block mb-4">
            <span className="text-sm font-medium text-gray-400 bg-gray-800 px-4 py-2 rounded-full border border-gray-700">
              Testimonials
            </span>
          </div>
          <h2 className="text-4xl lg:text-5xl font-bold mb-6">
            What Our Clients Say
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Don't just take our word for it. Here's what our amazing clients have to say about working with us.
          </p>
        </div>

        {/* Main Testimonial */}
        <div className="max-w-4xl mx-auto mb-16">
          <div className="bg-gray-800 rounded-3xl p-8 lg:p-12 border border-gray-700 relative">
            {/* Quote Icon */}
            <div className="text-6xl text-green-400 opacity-20 absolute top-6 left-6">
              "
            </div>
            
            {/* Rating Stars */}
            <div className="flex justify-center mb-6">
              {[...Array(currentClient.rating)].map((_, i) => (
                <svg key={i} className="w-6 h-6 text-yellow-400 fill-current" viewBox="0 0 24 24">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
              ))}
            </div>

            {/* Testimonial Text */}
            <blockquote className="text-xl lg:text-2xl text-center mb-8 leading-relaxed relative z-10">
              {currentClient.text}
            </blockquote>

            {/* Client Info */}
            <div className="flex items-center justify-center gap-4">
              <img 
                src={currentClient.image} 
                alt={currentClient.name}
                className="w-16 h-16 rounded-full border-2 border-green-400"
              />
              <div className="text-center">
                <h4 className="font-bold text-lg">{currentClient.name}</h4>
                <p className="text-gray-400">{currentClient.role}</p>
                <p className="text-green-400 text-sm">{currentClient.company}</p>
              </div>
            </div>

            {/* Project Stats */}
            <div className="flex justify-center gap-8 mt-6 pt-6 border-t border-gray-700">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-400">{currentClient.videoViews}</div>
                <div className="text-sm text-gray-400">Video Views</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold">{currentClient.project}</div>
                <div className="text-sm text-gray-400">Project Type</div>
              </div>
            </div>

            {/* Navigation Arrows */}
            <button 
              onClick={prevTestimonial}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-gray-700 hover:bg-green-400 hover:text-black w-12 h-12 rounded-full flex items-center justify-center transition-all duration-300"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <button 
              onClick={nextTestimonial}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-gray-700 hover:bg-green-400 hover:text-black w-12 h-12 rounded-full flex items-center justify-center transition-all duration-300"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </div>

        {/* Testimonial Dots */}
        <div className="flex justify-center gap-3 mb-16">
          {testimonials.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentTestimonial(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index === currentTestimonial ? 'bg-green-400' : 'bg-gray-600 hover:bg-gray-500'
              }`}
            />
          ))}
        </div>

        {/* All Testimonials Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {testimonials.map((testimonial, index) => (
            <div 
              key={testimonial.id}
              className={`bg-gray-800 rounded-2xl p-6 border transition-all duration-300 hover:transform hover:scale-105 cursor-pointer ${
                index === currentTestimonial ? 'border-green-400' : 'border-gray-700 hover:border-gray-600'
              }`}
              onClick={() => setCurrentTestimonial(index)}
            >
              {/* Rating */}
              <div className="flex mb-4">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <svg key={i} className="w-4 h-4 text-yellow-400 fill-current" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                ))}
              </div>

              {/* Short Text */}
              <p className="text-gray-300 mb-4 text-sm leading-relaxed line-clamp-3">
                {testimonial.text.substring(0, 120)}...
              </p>

              {/* Client Info */}
              <div className="flex items-center gap-3">
                <img 
                  src={testimonial.image} 
                  alt={testimonial.name}
                  className="w-10 h-10 rounded-full"
                />
                <div>
                  <h5 className="font-semibold text-sm">{testimonial.name}</h5>
                  <p className="text-gray-400 text-xs">{testimonial.company}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
