import React, { useState } from 'react';

const TestimonialsSection = () => {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);

  const testimonials = [
    {
      id: 1,
      name: "<PERSON>",
      role: "YouTube Creator",
      company: "Adventure Vlogs",
      image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=80&h=80&fit=crop&crop=face",
      rating: 5,
      text: "They transformed my raw clips into a professional YouTube video that got 5x more engagement! The editing style perfectly matched my brand, and the turnaround was incredibly fast.",
      videoViews: "2.5M",
      project: "Travel Vlog Series",
      category: "YouTube"
    },
    {
      id: 2,
      name: "<PERSON>",
      role: "Marketing Director",
      company: "TechStart Inc.",
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=80&h=80&fit=crop&crop=face",
      rating: 5,
      text: "Outstanding work on our product launch video. The team understood our vision immediately and delivered a cinematic piece that exceeded our expectations. Our conversion rate increased by 40%!",
      videoViews: "850K",
      project: "Product Launch Campaign",
      category: "Corporate"
    },
    {
      id: 3,
      name: "<PERSON>",
      role: "Content Creator",
      company: "Fashion Forward",
      image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=80&h=80&fit=crop&crop=face",
      rating: 5,
      text: "The reels they edited for us went viral! Their understanding of current trends and ability to create engaging content is unmatched. We've seen a 300% increase in followers.",
      videoViews: "1.2M",
      project: "Instagram Reels Campaign",
      category: "Social Media"
    },
    {
      id: 4,
      name: "David Thompson",
      role: "CEO",
      company: "Business Solutions Ltd.",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=80&fit=crop&crop=face",
      rating: 5,
      text: "Professional, reliable, and creative. They handled our corporate video with such attention to detail. The final product perfectly represented our brand values and professionalism.",
      videoViews: "450K",
      project: "Corporate Brand Video",
      category: "Corporate"
    },
    {
      id: 5,
      name: "Lisa Park",
      role: "Event Coordinator",
      company: "Dream Weddings",
      image: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=80&h=80&fit=crop&crop=face",
      rating: 5,
      text: "They captured the magic of our wedding perfectly! The highlight reel brought tears to our eyes. Every important moment was beautifully edited with perfect music synchronization.",
      videoViews: "125K",
      project: "Wedding Highlight Reel",
      category: "Events"
    },
    {
      id: 6,
      name: "Alex Kumar",
      role: "Gaming Streamer",
      company: "GameMaster Pro",
      image: "https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=80&h=80&fit=crop&crop=face",
      rating: 5,
      text: "Best gaming content editors I've worked with! They know exactly how to cut highlights that keep viewers engaged. My average watch time increased by 60% after working with them.",
      videoViews: "3.2M",
      project: "Gaming Highlights Series",
      category: "Gaming"
    }
  ];

  const nextTestimonial = () => {
    setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  const currentClient = testimonials[currentTestimonial];

  return (
    <section id="testimonials" className="py-20 bg-white">
      <div className="container mx-auto px-6 max-w-7xl">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-blue-50 text-blue-600 px-4 py-2 rounded-full text-sm font-medium mb-4">
            <span>💬</span>
            Testimonials
          </div>
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            What Our Clients Say
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Don't just take our word for it. Here's what our amazing clients have to say about working with us and the results they've achieved.
          </p>
        </div>

        {/* Main Testimonial */}
        <div className="max-w-5xl mx-auto mb-16">
          <div className="bg-gray-50 rounded-3xl p-8 lg:p-12 relative overflow-hidden">
            {/* Background Pattern */}
            <div className="absolute top-0 right-0 w-32 h-32 bg-blue-100 rounded-full -translate-y-16 translate-x-16 opacity-50"></div>
            <div className="absolute bottom-0 left-0 w-24 h-24 bg-indigo-100 rounded-full translate-y-12 -translate-x-12 opacity-50"></div>

            {/* Quote Icon */}
            <div className="text-6xl text-blue-200 opacity-50 absolute top-6 left-6">
              "
            </div>

            {/* Category Badge */}
            <div className="flex justify-center mb-6">
              <span className={`px-4 py-2 rounded-full text-sm font-medium ${
                currentClient.category === 'YouTube' ? 'bg-red-100 text-red-600' :
                currentClient.category === 'Corporate' ? 'bg-blue-100 text-blue-600' :
                currentClient.category === 'Social Media' ? 'bg-pink-100 text-pink-600' :
                currentClient.category === 'Gaming' ? 'bg-purple-100 text-purple-600' :
                'bg-green-100 text-green-600'
              }`}>
                {currentClient.category}
              </span>
            </div>

            {/* Rating Stars */}
            <div className="flex justify-center mb-8">
              {[...Array(currentClient.rating)].map((_, i) => (
                <svg key={i} className="w-6 h-6 text-yellow-400 fill-current mx-1" viewBox="0 0 24 24">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
              ))}
            </div>

            {/* Testimonial Text */}
            <blockquote className="text-xl lg:text-2xl text-center text-gray-800 mb-8 leading-relaxed relative z-10 font-medium">
              "{currentClient.text}"
            </blockquote>

            {/* Client Info */}
            <div className="flex flex-col sm:flex-row items-center justify-center gap-6 mb-8">
              <img
                src={currentClient.image}
                alt={currentClient.name}
                className="w-20 h-20 rounded-full border-4 border-white shadow-lg"
              />
              <div className="text-center sm:text-left">
                <h4 className="font-bold text-xl text-gray-900">{currentClient.name}</h4>
                <p className="text-gray-600 font-medium">{currentClient.role}</p>
                <p className="text-blue-600 text-sm font-semibold">{currentClient.company}</p>
              </div>
            </div>

            {/* Project Stats */}
            <div className="flex flex-col sm:flex-row justify-center gap-8 pt-8 border-t border-gray-200">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600">{currentClient.videoViews}</div>
                <div className="text-sm text-gray-500 font-medium">Video Views</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-gray-900">{currentClient.project}</div>
                <div className="text-sm text-gray-500 font-medium">Project Type</div>
              </div>
            </div>

            {/* Navigation Arrows */}
            <button
              onClick={prevTestimonial}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white hover:bg-blue-600 hover:text-white w-12 h-12 rounded-full flex items-center justify-center transition-all duration-300 shadow-lg border border-gray-200"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <button
              onClick={nextTestimonial}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white hover:bg-blue-600 hover:text-white w-12 h-12 rounded-full flex items-center justify-center transition-all duration-300 shadow-lg border border-gray-200"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </div>

        {/* Testimonial Dots */}
        <div className="flex justify-center gap-3 mb-16">
          {testimonials.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentTestimonial(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index === currentTestimonial ? 'bg-blue-600' : 'bg-gray-300 hover:bg-gray-400'
              }`}
            />
          ))}
        </div>

        {/* All Testimonials Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-16">
          {testimonials.map((testimonial, index) => (
            <div
              key={testimonial.id}
              className={`bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:transform hover:scale-105 cursor-pointer border-2 ${
                index === currentTestimonial ? 'border-blue-500' : 'border-gray-100 hover:border-gray-200'
              }`}
              onClick={() => setCurrentTestimonial(index)}
            >
              {/* Category Badge */}
              <div className="mb-4">
                <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                  testimonial.category === 'YouTube' ? 'bg-red-100 text-red-600' :
                  testimonial.category === 'Corporate' ? 'bg-blue-100 text-blue-600' :
                  testimonial.category === 'Social Media' ? 'bg-pink-100 text-pink-600' :
                  testimonial.category === 'Gaming' ? 'bg-purple-100 text-purple-600' :
                  'bg-green-100 text-green-600'
                }`}>
                  {testimonial.category}
                </span>
              </div>

              {/* Rating */}
              <div className="flex mb-4">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <svg key={i} className="w-4 h-4 text-yellow-400 fill-current" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                ))}
              </div>

              {/* Short Text */}
              <p className="text-gray-600 mb-4 text-sm leading-relaxed">
                "{testimonial.text.substring(0, 120)}..."
              </p>

              {/* Client Info */}
              <div className="flex items-center gap-3">
                <img
                  src={testimonial.image}
                  alt={testimonial.name}
                  className="w-12 h-12 rounded-full border-2 border-gray-200"
                />
                <div>
                  <h5 className="font-semibold text-sm text-gray-900">{testimonial.name}</h5>
                  <p className="text-gray-500 text-xs">{testimonial.role}</p>
                  <p className="text-blue-600 text-xs font-medium">{testimonial.company}</p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center bg-gradient-to-r from-blue-600 to-indigo-600 rounded-3xl p-12 text-white">
          <h3 className="text-3xl font-bold mb-4">
            Ready to Join Our Success Stories?
          </h3>
          <p className="text-blue-100 mb-8 max-w-2xl mx-auto text-lg">
            Let's create something amazing together and add your testimonial to our growing list of satisfied clients.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-300">
              Start Your Project
            </button>
            <button className="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-all duration-300">
              Schedule Consultation
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
