import React from 'react';

const WhyChooseUsSection = () => {
  const features = [
    {
      icon: "⚡",
      title: "Lightning Fast Turnaround",
      description: "Get your professionally edited videos back in 24-48 hours without compromising on quality or creativity.",
      highlight: "24-48 hours delivery",
      color: "bg-yellow-50 text-yellow-600"
    },
    {
      icon: "🔄",
      title: "Unlimited Revisions",
      description: "We work tirelessly until you're 100% satisfied. Your vision, our expertise, perfect results every time.",
      highlight: "Until you're happy",
      color: "bg-green-50 text-green-600"
    },
    {
      icon: "🎯",
      title: "Professional Tools & Expertise",
      description: "We use industry-standard software like Adobe Premiere Pro, After Effects, and DaVinci Resolve for premium results.",
      highlight: "Professional grade",
      color: "bg-blue-50 text-blue-600"
    },
    {
      icon: "👥",
      title: "Dedicated Creative Team",
      description: "Work directly with experienced editors who understand your brand, audience, and creative vision.",
      highlight: "Expert editors",
      color: "bg-purple-50 text-purple-600"
    },
    {
      icon: "📞",
      title: "24/7 Support & Communication",
      description: "Seamless communication through your preferred channels. We're always here when you need us.",
      highlight: "Always available",
      color: "bg-pink-50 text-pink-600"
    },
    {
      icon: "💎",
      title: "Premium Quality Guarantee",
      description: "Every project meets our high standards for creativity, technical excellence, and storytelling impact.",
      highlight: "Quality assured",
      color: "bg-indigo-50 text-indigo-600"
    }
  ];

  const stats = [
    { number: "500+", label: "Projects Completed", icon: "🎬" },
    { number: "98%", label: "Client Satisfaction", icon: "⭐" },
    { number: "24hrs", label: "Average Turnaround", icon: "⚡" },
    { number: "50+", label: "Happy Clients", icon: "😊" }
  ];

  return (
    <section id="why-choose-us" className="py-20 bg-gray-50">
      <div className="container mx-auto px-6 max-w-7xl">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-blue-50 text-blue-600 px-4 py-2 rounded-full text-sm font-medium mb-4">
            <span>🏆</span>
            Why Choose Us
          </div>
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Why Work With Us?
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            We're not just editors – we're your creative partners committed to bringing your vision to life with excellence, efficiency, and unmatched quality.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {features.map((feature, index) => (
            <div
              key={index}
              className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 group hover:transform hover:scale-105 border border-gray-100"
            >
              {/* Icon */}
              <div className="text-5xl mb-6 group-hover:scale-110 transition-transform duration-300">
                {feature.icon}
              </div>

              {/* Title */}
              <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors duration-300">
                {feature.title}
              </h3>

              {/* Highlight Badge */}
              <div className={`inline-block px-3 py-1 rounded-full text-sm font-semibold mb-4 ${feature.color}`}>
                {feature.highlight}
              </div>

              {/* Description */}
              <p className="text-gray-600 leading-relaxed">
                {feature.description}
              </p>
            </div>
          ))}
        </div>

        {/* Stats Section */}
        <div className="bg-white rounded-3xl p-12 shadow-lg border border-gray-100 mb-20">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">Our Track Record Speaks</h3>
            <p className="text-gray-600 text-lg">Numbers that showcase our commitment to excellence and client satisfaction</p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center group">
                <div className="text-3xl mb-2 group-hover:scale-110 transition-transform duration-300">
                  {stat.icon}
                </div>
                <div className="text-4xl lg:text-5xl font-bold text-blue-600 mb-2">
                  {stat.number}
                </div>
                <div className="text-gray-600 font-medium">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Process Timeline */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-3xl p-12 text-white">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold mb-4">Our Simple Process</h3>
            <p className="text-blue-100 text-lg">From concept to completion in just four easy steps</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {[
              { step: "01", title: "Brief & Upload", desc: "Share your vision and upload your raw footage securely", icon: "📤" },
              { step: "02", title: "Edit & Create", desc: "Our expert team works their magic on your content", icon: "✂️" },
              { step: "03", title: "Review & Revise", desc: "You review and request any changes needed", icon: "👀" },
              { step: "04", title: "Final Delivery", desc: "Receive your polished video ready to publish", icon: "🚀" }
            ].map((process, index) => (
              <div key={index} className="text-center relative">
                {index < 3 && (
                  <div className="hidden md:block absolute top-8 left-full w-full h-0.5 bg-gradient-to-r from-white/30 to-transparent"></div>
                )}
                <div className="bg-white text-blue-600 w-16 h-16 rounded-full flex items-center justify-center font-bold text-lg mx-auto mb-4 shadow-lg">
                  {process.step}
                </div>
                <div className="text-3xl mb-3">{process.icon}</div>
                <h4 className="text-xl font-bold mb-2">{process.title}</h4>
                <p className="text-blue-100 text-sm">{process.desc}</p>
              </div>
            ))}
          </div>

          {/* CTA Section */}
          <div className="text-center mt-12 pt-12 border-t border-white/20">
            <h4 className="text-2xl font-bold mb-4">Ready to Transform Your Content?</h4>
            <p className="text-blue-100 mb-8 max-w-2xl mx-auto">
              Join hundreds of satisfied clients who trust us with their video content. Let's create something amazing together.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-300">
                Start Your Project
              </button>
              <button className="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-all duration-300">
                Schedule a Call
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhyChooseUsSection;
