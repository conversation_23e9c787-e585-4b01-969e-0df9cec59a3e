import React from 'react';

const WhyChooseUsSection = () => {
  const features = [
    {
      icon: "⚡",
      title: "Fast Turnaround Times",
      description: "Get your edited videos back in 24-48 hours without compromising on quality.",
      highlight: "24-48 hours delivery"
    },
    {
      icon: "🔄",
      title: "Unlimited Revisions",
      description: "We work until you're 100% satisfied. Your vision, perfectly executed.",
      highlight: "Until you're happy"
    },
    {
      icon: "🎯",
      title: "Industry-Standard Software",
      description: "We use professional tools like Adobe Premiere Pro, After Effects, and DaVinci Resolve.",
      highlight: "Professional tools"
    },
    {
      icon: "👥",
      title: "Dedicated Creative Team",
      description: "Work with experienced editors who understand your brand and audience.",
      highlight: "Expert editors"
    },
    {
      icon: "📞",
      title: "24/7 Support & Communication",
      description: "Easy communication through your preferred channels. We're always here to help.",
      highlight: "Always available"
    }
  ];

  const stats = [
    { number: "500+", label: "Projects Completed" },
    { number: "98%", label: "Client Satisfaction" },
    { number: "24hrs", label: "Average Turnaround" },
    { number: "50+", label: "Happy Clients" }
  ];

  return (
    <section id="why-choose-us" className="py-20 bg-gradient-to-br from-gray-900 via-black to-gray-900 text-white">
      <div className="container mx-auto px-6 max-w-7xl">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-block mb-4">
            <span className="text-sm font-medium text-gray-400 bg-gray-800 px-4 py-2 rounded-full border border-gray-700">
              Why Choose Us
            </span>
          </div>
          <h2 className="text-4xl lg:text-5xl font-bold mb-6">
            Why Work With Us?
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            We're not just editors – we're your creative partners committed to bringing your vision to life with excellence and efficiency.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {features.map((feature, index) => (
            <div 
              key={index}
              className="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-8 border border-gray-700 hover:border-green-400 transition-all duration-300 group hover:transform hover:scale-105"
            >
              {/* Icon */}
              <div className="text-5xl mb-6 group-hover:scale-110 transition-transform duration-300">
                {feature.icon}
              </div>

              {/* Title */}
              <h3 className="text-2xl font-bold mb-4 group-hover:text-green-400 transition-colors duration-300">
                {feature.title}
              </h3>

              {/* Highlight */}
              <div className="inline-block bg-green-400/20 text-green-400 px-3 py-1 rounded-full text-sm font-semibold mb-4">
                {feature.highlight}
              </div>

              {/* Description */}
              <p className="text-gray-300 leading-relaxed">
                {feature.description}
              </p>
            </div>
          ))}

          {/* Special CTA Card */}
          <div className="bg-gradient-to-br from-green-400 to-blue-500 rounded-2xl p-8 text-black hover:transform hover:scale-105 transition-all duration-300">
            <div className="text-5xl mb-6">🚀</div>
            <h3 className="text-2xl font-bold mb-4">
              Ready to Get Started?
            </h3>
            <p className="mb-6 opacity-80">
              Join hundreds of satisfied clients who trust us with their video content.
            </p>
            <button className="w-full bg-black text-white py-3 px-6 rounded-lg font-semibold hover:bg-gray-800 transition-colors duration-300">
              Start Your Project
            </button>
          </div>
        </div>

        {/* Stats Section */}
        <div className="bg-gray-800/30 backdrop-blur-sm rounded-3xl p-12 border border-gray-700">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold mb-4">Our Track Record Speaks</h3>
            <p className="text-gray-300 text-lg">Numbers that showcase our commitment to excellence</p>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-4xl lg:text-5xl font-bold text-green-400 mb-2">
                  {stat.number}
                </div>
                <div className="text-gray-300 font-medium">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Process Timeline */}
        <div className="mt-20">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold mb-4">Our Simple Process</h3>
            <p className="text-gray-300 text-lg">From brief to final delivery in just a few steps</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {[
              { step: "01", title: "Brief & Upload", desc: "Share your vision and upload raw footage" },
              { step: "02", title: "Edit & Create", desc: "Our team works their magic on your content" },
              { step: "03", title: "Review & Revise", desc: "You review and request any changes needed" },
              { step: "04", title: "Final Delivery", desc: "Receive your polished video ready to publish" }
            ].map((process, index) => (
              <div key={index} className="text-center relative">
                {index < 3 && (
                  <div className="hidden md:block absolute top-8 left-full w-full h-0.5 bg-gradient-to-r from-green-400 to-transparent"></div>
                )}
                <div className="bg-green-400 text-black w-16 h-16 rounded-full flex items-center justify-center font-bold text-lg mx-auto mb-4">
                  {process.step}
                </div>
                <h4 className="text-xl font-bold mb-2">{process.title}</h4>
                <p className="text-gray-300">{process.desc}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhyChooseUsSection;
