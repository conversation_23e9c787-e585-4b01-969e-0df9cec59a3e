import React from 'react'
import Navbar from './components/Navbar'
import HeroSection from './components/HeroSection'
import './output.css'

function App() {
  return (
    <div className="App">
      <Navbar />
      <HeroSection />
      <section id="about" style={{ height: '100vh', padding: '2rem', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <h2 className='text-red-500'>About Us Section</h2>
      </section>

      <section id="services" style={{ height: '100vh', padding: '2rem', display: 'flex', alignItems: 'center', justifyContent: 'center', backgroundColor: '#253900' }}>
        <h2>Services Section</h2>
      </section>

      <section id="portfolio" style={{ height: '100vh', padding: '2rem', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <h2>Portfolio Section</h2>
      </section>

      <section id="why-choose-us" style={{ height: '100vh', padding: '2rem', display: 'flex', alignItems: 'center', justifyContent: 'center', backgroundColor: '#EEEEEE', color: '#000' }}>
        <h2>Why Choose Us Section</h2>
      </section>

      <section id="testimonials" style={{ height: '100vh', padding: '2rem', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <h2>Testimonials Section</h2>
      </section>

      <section id="contact" style={{ height: '100vh', padding: '2rem', display: 'flex', alignItems: 'center', justifyContent: 'center', backgroundColor: '#08CB00', color: '#000' }}>
        <h2>Contact Section</h2>
      </section>
    </div>
  )
}

export default App
