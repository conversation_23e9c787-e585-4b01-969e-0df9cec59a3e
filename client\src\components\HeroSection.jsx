import React, { useEffect, useRef } from "react";
import "./HeroSection.css";

const HeroSection = () => {
  const videoRef = useRef(null);

  useEffect(() => {
    // Auto-play video when component mounts
    if (videoRef.current) {
      videoRef.current.play().catch(console.error);
    }
  }, []);

  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
    }
  };

  return (
    <section id="home" className="hero-section">
      {/* Background Video */}
      <div className="hero-background">
        {/* Placeholder for video - replace with actual video later */}
        {/* <div className="hero-video-placeholder">
          <div className="video-placeholder-content">
            <div className="placeholder-icon">🎬</div>
            <p>Video Background Placeholder</p>
            <small>Replace with your video editing reel</small>
          </div>
        </div> */}
        <div className="hero-overlay"></div>
      </div>

      {/* Hero Content */}
      <div className="hero-content">
        <div className="container">
          <div className="hero-text">
            {/* Badge */}
            {/* <div className="hero-badge">
              <span className="badge-icon">🎬</span>
              <span className="badge-text text-red-500">Video Editing Experts</span>
            </div> */}

            {/* Main Headline */}
            <h1 className="hero-headline text-red-500">
             Captivating Stories, One Frame at a Time.
            </h1>

            {/* Subheadline */}
            <p className="hero-subheadline">
              We help brands, creators, and businesses turn raw footage into
              cinematic experiences that engage and convert.
            </p>

            {/* CTA Buttons */}
            <div className="hero-cta">
              <button
                className="cta-primary"
                onClick={() => scrollToSection("portfolio")}
              >
                <span>View Our Work</span>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path
                    d="M7 17L17 7M17 7H7M17 7V17"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </button>

              <button
                className="cta-secondary"
                onClick={() => scrollToSection("contact")}
              >
                <span>Get a Free Quote</span>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path
                    d="M22 12H18L15 21L9 3L6 12H2"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </button>
            </div>

            {/* Stats or Trust Indicators */}
            {/* <div className="hero-stats">
              <div className="stat-item">
                <span className="stat-number">500+</span>
                <span className="stat-label">Projects Completed</span>
              </div>
              <div className="stat-item">
                <span className="stat-number">50+</span>
                <span className="stat-label">Happy Clients</span>
              </div>
              <div className="stat-item">
                <span className="stat-number">24/7</span>
                <span className="stat-label">Support Available</span>
              </div>
            </div> */}
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="scroll-indicator">
        <div className="scroll-mouse">
          <div className="scroll-wheel"></div>
        </div>
        <span className="scroll-text">Scroll to explore</span>
      </div>
    </section>
  );
};

export default HeroSection;
