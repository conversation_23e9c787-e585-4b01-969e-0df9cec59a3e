import React from 'react';

const ServicesSection = () => {
  const services = [
    {
      icon: "🎬",
      title: "YouTube Video Editing",
      description: "From vlogs to cinematic content.",
      features: ["Professional cuts", "Color grading", "Audio enhancement", "Thumbnail design"]
    },
    {
      icon: "📱",
      title: "Reels & Shorts Editing",
      description: "High-retention, trending style edits.",
      features: ["Viral hooks", "Quick transitions", "Trending effects", "Optimized for mobile"]
    },
    {
      icon: "🏢",
      title: "Corporate & Business Videos",
      description: "Professional brand storytelling.",
      features: ["Brand consistency", "Professional tone", "Clear messaging", "Executive interviews"]
    },
    {
      icon: "📺",
      title: "Ad Films & Commercials",
      description: "Conversion-focused editing.",
      features: ["Call-to-action focus", "Brand integration", "Emotional storytelling", "Performance tracking"]
    },
    {
      icon: "🎉",
      title: "Event Highlight Videos",
      description: "Weddings, conferences, launches.",
      features: ["Key moments capture", "Emotional storytelling", "Multi-camera editing", "Music synchronization"]
    },
    {
      icon: "✨",
      title: "Motion Graphics & VFX",
      description: "Bring ideas to life with animation.",
      features: ["Custom animations", "Visual effects", "Logo animations", "Infographic videos"]
    }
  ];

  return (
    <section id="services" className="py-20 bg-gray-900 text-white">
      <div className="container mx-auto px-6 max-w-7xl">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-block mb-4">
            <span className="text-sm font-medium text-gray-400 bg-gray-800 px-4 py-2 rounded-full border border-gray-700">
              Services
            </span>
          </div>
          <h2 className="text-4xl lg:text-5xl font-bold mb-6">
            What We Do
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            We specialize in transforming raw footage into compelling stories that engage, inspire, and convert your audience.
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <div 
              key={index}
              className="bg-gray-800 rounded-2xl p-8 border border-gray-700 hover:border-green-400 transition-all duration-300 group hover:transform hover:scale-105"
            >
              {/* Icon */}
              <div className="text-5xl mb-6 group-hover:scale-110 transition-transform duration-300">
                {service.icon}
              </div>

              {/* Title */}
              <h3 className="text-2xl font-bold mb-4 group-hover:text-green-400 transition-colors duration-300">
                {service.title}
              </h3>

              {/* Description */}
              <p className="text-gray-300 mb-6 text-lg">
                {service.description}
              </p>

              {/* Features */}
              <ul className="space-y-3">
                {service.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-green-400 rounded-full flex-shrink-0"></div>
                    <span className="text-gray-300">{feature}</span>
                  </li>
                ))}
              </ul>

              {/* CTA Button */}
              <div className="mt-8">
                <button className="w-full bg-transparent border-2 border-green-400 text-green-400 py-3 px-6 rounded-lg font-semibold hover:bg-green-400 hover:text-black transition-all duration-300">
                  Learn More
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="bg-gradient-to-r from-green-400 to-blue-500 rounded-2xl p-8 max-w-4xl mx-auto">
            <h3 className="text-3xl font-bold text-black mb-4">
              Ready to Transform Your Content?
            </h3>
            <p className="text-black/80 text-lg mb-6">
              Let's discuss your project and create something amazing together.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-black text-white px-8 py-3 rounded-lg font-semibold hover:bg-gray-800 transition-colors duration-300">
                Start a Project
              </button>
              <button className="bg-transparent border-2 border-black text-black px-8 py-3 rounded-lg font-semibold hover:bg-black hover:text-white transition-all duration-300">
                Schedule a Call
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;
