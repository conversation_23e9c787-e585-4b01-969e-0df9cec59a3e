import React from 'react';

const ServicesSection = () => {
  const services = [
    {
      icon: "🎬",
      title: "YouTube Video Editing",
      description: "Transform your vlogs and content into professional, engaging videos that captivate your audience.",
      features: ["Professional cuts & transitions", "Color grading & correction", "Audio enhancement", "Thumbnail design"],
      popular: true
    },
    {
      icon: "📱",
      title: "Reels & Shorts Editing",
      description: "Create viral-worthy short-form content optimized for maximum engagement and retention.",
      features: ["Viral hooks & openings", "Quick dynamic transitions", "Trending effects & filters", "Mobile-first optimization"],
      popular: false
    },
    {
      icon: "🏢",
      title: "Corporate & Business Videos",
      description: "Professional brand storytelling that communicates your message with clarity and impact.",
      features: ["Brand consistency", "Professional tone & style", "Clear messaging", "Executive interviews"],
      popular: false
    },
    {
      icon: "📺",
      title: "Ad Films & Commercials",
      description: "Conversion-focused video ads designed to drive results and maximize your ROI.",
      features: ["Call-to-action focus", "Brand integration", "Emotional storytelling", "Performance optimization"],
      popular: false
    },
    {
      icon: "🎉",
      title: "Event Highlight Videos",
      description: "Capture and preserve your special moments with cinematic highlight reels.",
      features: ["Key moments capture", "Emotional storytelling", "Multi-camera editing", "Music synchronization"],
      popular: false
    },
    {
      icon: "✨",
      title: "Motion Graphics & VFX",
      description: "Bring your ideas to life with stunning animations and visual effects.",
      features: ["Custom animations", "Visual effects", "Logo animations", "Infographic videos"],
      popular: false
    }
  ];

  return (
    <section id="services" className="py-20 bg-gray-50">
      <div className="container mx-auto px-6 max-w-7xl">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-blue-50 text-blue-600 px-4 py-2 rounded-full text-sm font-medium mb-4">
            <span>⚡</span>
            Our Services
          </div>
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            What We Do
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            We specialize in transforming raw footage into compelling stories that engage, inspire, and convert your audience across all platforms.
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {services.map((service, index) => (
            <div
              key={index}
              className={`bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 group hover:transform hover:scale-105 relative ${
                service.popular ? 'ring-2 ring-blue-500' : 'border border-gray-200'
              }`}
            >
              {/* Popular Badge */}
              {service.popular && (
                <div className="absolute -top-3 left-6 bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                  Most Popular
                </div>
              )}

              {/* Icon */}
              <div className="text-5xl mb-6 group-hover:scale-110 transition-transform duration-300">
                {service.icon}
              </div>

              {/* Title */}
              <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors duration-300">
                {service.title}
              </h3>

              {/* Description */}
              <p className="text-gray-600 mb-6 leading-relaxed">
                {service.description}
              </p>

              {/* Features */}
              <ul className="space-y-3 mb-8">
                {service.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-start gap-3">
                    <div className="w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                    </div>
                    <span className="text-gray-700 text-sm">{feature}</span>
                  </li>
                ))}
              </ul>

              {/* CTA Button */}
              <button className={`w-full py-3 px-6 rounded-lg font-semibold transition-all duration-300 ${
                service.popular
                  ? 'bg-blue-600 text-white hover:bg-blue-700'
                  : 'border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white'
              }`}>
                Get Started
              </button>
            </div>
          ))}
        </div>

        {/* Process Section */}
        <div className="bg-white rounded-3xl p-12 shadow-lg">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">Our Simple Process</h3>
            <p className="text-gray-600">From concept to completion in just a few easy steps</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {[
              { step: "01", title: "Brief & Upload", desc: "Share your vision and upload your raw footage", icon: "📤" },
              { step: "02", title: "Edit & Create", desc: "Our expert team works their magic on your content", icon: "✂️" },
              { step: "03", title: "Review & Revise", desc: "You review and request any changes needed", icon: "👀" },
              { step: "04", title: "Final Delivery", desc: "Receive your polished video ready to publish", icon: "🚀" }
            ].map((process, index) => (
              <div key={index} className="text-center relative">
                {index < 3 && (
                  <div className="hidden md:block absolute top-8 left-full w-full h-0.5 bg-gradient-to-r from-blue-200 to-transparent"></div>
                )}
                <div className="bg-blue-600 text-white w-16 h-16 rounded-full flex items-center justify-center font-bold text-lg mx-auto mb-4">
                  {process.step}
                </div>
                <div className="text-3xl mb-3">{process.icon}</div>
                <h4 className="text-xl font-bold text-gray-900 mb-2">{process.title}</h4>
                <p className="text-gray-600 text-sm">{process.desc}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-3xl p-12 text-white">
            <h3 className="text-3xl font-bold mb-4">
              Ready to Transform Your Content?
            </h3>
            <p className="text-blue-100 text-lg mb-8 max-w-2xl mx-auto">
              Let's discuss your project and create something amazing together. Get started with a free consultation today.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-300">
                Start Your Project
              </button>
              <button className="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-all duration-300">
                Schedule a Call
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;
