import React from 'react';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  const quickLinks = [
    { name: 'Home', href: '#home' },
    { name: 'About', href: '#about' },
    { name: 'Services', href: '#services' },
    { name: 'Portfolio', href: '#portfolio' },
    { name: 'Testimonials', href: '#testimonials' },
    { name: 'Contact', href: '#contact' }
  ];

  const services = [
    'YouTube Video Editing',
    'Reels & Shorts Editing',
    'Corporate Videos',
    'Ad Films & Commercials',
    'Event Highlights',
    'Motion Graphics & VFX'
  ];

  const socialLinks = [
    {
      name: 'YouTube',
      icon: '📺',
      href: '#',
      color: 'hover:text-red-500'
    },
    {
      name: 'Instagram',
      icon: '📷',
      href: '#',
      color: 'hover:text-pink-500'
    },
    {
      name: 'LinkedIn',
      icon: '💼',
      href: '#',
      color: 'hover:text-blue-500'
    },
    {
      name: 'Twitter',
      icon: '🐦',
      href: '#',
      color: 'hover:text-blue-400'
    },
    {
      name: 'TikTok',
      icon: '🎵',
      href: '#',
      color: 'hover:text-white'
    },
    {
      name: 'Discord',
      icon: '🎮',
      href: '#',
      color: 'hover:text-purple-500'
    }
  ];

  return (
    <footer className="bg-black text-white border-t border-gray-800">
      {/* Main Footer Content */}
      <div className="container mx-auto px-6 py-16 max-w-7xl">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12">
          {/* Company Info */}
          <div className="lg:col-span-1">
            <div className="mb-6">
              {/* Logo */}
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 bg-gradient-to-r from-green-400 to-blue-500 rounded-lg flex items-center justify-center">
                  <span className="text-black font-bold text-xl">🎬</span>
                </div>
                <span className="text-2xl font-bold">VideoEditors</span>
              </div>
              <p className="text-gray-300 leading-relaxed mb-6">
                Transforming raw footage into compelling stories that engage, inspire, and convert your audience. Professional video editing services for creators and businesses worldwide.
              </p>
            </div>

            {/* Contact Info */}
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <span className="text-green-400">📧</span>
                <span className="text-gray-300"><EMAIL></span>
              </div>
              <div className="flex items-center gap-3">
                <span className="text-green-400">⚡</span>
                <span className="text-gray-300">24/7 Support Available</span>
              </div>
              <div className="flex items-center gap-3">
                <span className="text-green-400">🌍</span>
                <span className="text-gray-300">Serving Clients Worldwide</span>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-xl font-bold mb-6">Quick Links</h3>
            <ul className="space-y-3">
              {quickLinks.map((link, index) => (
                <li key={index}>
                  <a 
                    href={link.href}
                    className="text-gray-300 hover:text-green-400 transition-colors duration-300 flex items-center gap-2"
                  >
                    <span className="w-1 h-1 bg-green-400 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Services */}
          <div>
            <h3 className="text-xl font-bold mb-6">Our Services</h3>
            <ul className="space-y-3">
              {services.map((service, index) => (
                <li key={index}>
                  <span className="text-gray-300 hover:text-green-400 transition-colors duration-300 cursor-pointer">
                    {service}
                  </span>
                </li>
              ))}
            </ul>
          </div>

          {/* Newsletter & Social */}
          <div>
            <h3 className="text-xl font-bold mb-6">Stay Connected</h3>
            
            {/* Newsletter Signup */}
            <div className="mb-8">
              <p className="text-gray-300 mb-4">
                Get updates on our latest work and video editing tips.
              </p>
              <div className="flex gap-2">
                <input
                  type="email"
                  placeholder="Enter your email"
                  className="flex-1 bg-gray-800 border border-gray-700 rounded-lg px-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-green-400 focus:ring-1 focus:ring-green-400 transition-colors duration-300"
                />
                <button className="bg-green-400 text-black px-4 py-2 rounded-lg font-semibold hover:bg-green-300 transition-colors duration-300">
                  Subscribe
                </button>
              </div>
            </div>

            {/* Social Media Links */}
            <div>
              <h4 className="font-semibold mb-4">Follow Us</h4>
              <div className="flex gap-3">
                {socialLinks.map((social, index) => (
                  <a
                    key={index}
                    href={social.href}
                    className={`w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center transition-all duration-300 hover:bg-gray-700 hover:transform hover:scale-110 ${social.color}`}
                    title={social.name}
                  >
                    <span className="text-lg">{social.icon}</span>
                  </a>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-gray-800">
        <div className="container mx-auto px-6 py-6 max-w-7xl">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            {/* Copyright */}
            <div className="text-gray-400 text-sm">
              © {currentYear} VideoEditors. All rights reserved.
            </div>

            {/* Legal Links */}
            <div className="flex gap-6 text-sm">
              <a href="#" className="text-gray-400 hover:text-green-400 transition-colors duration-300">
                Privacy Policy
              </a>
              <a href="#" className="text-gray-400 hover:text-green-400 transition-colors duration-300">
                Terms of Service
              </a>
              <a href="#" className="text-gray-400 hover:text-green-400 transition-colors duration-300">
                Cookie Policy
              </a>
            </div>

            {/* Back to Top */}
            <button 
              onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
              className="bg-gray-800 hover:bg-green-400 hover:text-black w-10 h-10 rounded-lg flex items-center justify-center transition-all duration-300 hover:transform hover:scale-110"
              title="Back to Top"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Floating CTA */}
      <div className="fixed bottom-6 right-6 z-50">
        <button className="bg-gradient-to-r from-green-400 to-blue-500 text-black font-bold px-6 py-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 flex items-center gap-2">
          <span className="text-lg">🚀</span>
          <span className="hidden sm:inline">Start Project</span>
        </button>
      </div>
    </footer>
  );
};

export default Footer;
