export type Formatter = (input: string | number | null | undefined) => string

export interface Colors {
	isColorSupported: boolean

	reset: Formatter
	bold: Formatter
	dim: Formatter
	italic: Formatter
	underline: Formatter
	inverse: Formatter
	hidden: Formatter
	strikethrough: Formatter

	black: Formatter
	red: Formatter
	green: Formatter
	yellow: Formatter
	blue: Formatter
	magenta: Formatter
	cyan: Formatter
	white: Formatter
	gray: Formatter

	bgBlack: Formatter
	bgRed: Formatter
	bgGreen: Formatter
	bgYellow: Formatter
	bgBlue: Formatter
	bgMagenta: Formatter
	bgCyan: Formatter
	bgWhite: Formatter

	blackBright: Formatter
	redBright: Formatter
	greenBright: Formatter
	yellowBright: Formatter
	blueBright: Formatter
	magentaBright: Formatter
	cyanBright: Formatter
	whiteBright: Formatter

	bgBlackBright: Formatter
	bgRedBright: Formatter
	bgGreenBright: Formatter
	bgYellowBright: Formatter
	bgBlueBright: Formatter
	bgMagentaBright: Formatter
	bgCyanBright: Formatter
	bgWhiteBright: Formatter
}
